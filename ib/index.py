from ib_insync import *
import pandas as pd

ib = IB()
ib.connect('127.0.0.1', 74, clientId=1)

# Sottostante AAPL
aapl = Stock('AAPL', 'SMART', 'USD')
ticker_aapl = ib.reqMktData(aapl)
ib.sleep(2)
aapl_price = ticker_aapl.last if ticker_aapl.last else ticker_aapl.close

# Parametri opzioni
details = ib.reqContractDetails(aapl)
conId = details[0].contract.conId
chains = ib.reqSecDefOptParams(aapl.symbol, '', aapl.secType, conId)

# Seleziona solo la prima scadenza (per evitare overload)
chain = chains[0]
exp = sorted(chain.expirations)[0]

rows = []
for strike in chain.strikes[:5]:  # solo primi 5 strike
    for right in ['C', 'P']:
        option = Option(aapl.symbol, exp, strike, right, 'SMART')
        ticker = ib.reqMktData(option)
        ib.sleep(1)  # evita pacing violation
        rows.append({
            'Expiration': exp,
            'Strike': strike,
            'Right': right,
            'Bid': ticker.bid,
            'Ask': ticker.ask
        })

df = pd.DataFrame(rows)
df['UnderlyingPrice'] = aapl_price

print(df)

ib.disconnect()
