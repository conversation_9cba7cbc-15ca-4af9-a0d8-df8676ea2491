from ib_insync import *
import pandas as pd

# Connect to IB
ib = IB()
ib.connect('127.0.0.1', 7496, clientId=1)

# Define the contract for AAPL stock
stock = Stock('AAPL', 'SMART', 'USD')
ib.qualifyContracts(stock)

# Request market data for the stock to get current price
stock_ticker = ib.reqMktData(stock)
ib.sleep(2)  # Wait for stock data to arrive

# Get current stock price
if stock_ticker.last:
    current_stock_price = stock_ticker.last
elif stock_ticker.close:
    current_stock_price = stock_ticker.close
elif stock_ticker.bid and stock_ticker.ask:
    current_stock_price = (stock_ticker.bid + stock_ticker.ask) / 2
else:
    print("Could not get stock price")
    ib.disconnect()
    exit()

print(f"Current AAPL price: ${current_stock_price:.2f}")

# Request all available option chains
chains = ib.reqSecDefOptParams(stock.symbol, '', stock.secType, stock.conId)

# Initialize lists to store option data
all_options = []

# Process each option chain (limit to reasonable strikes near current price)
for chain in chains:
    # Filter strikes to reasonable range (within 20% of current price)
    reasonable_strikes = [s for s in chain.strikes if abs(s - current_stock_price) / current_stock_price <= 0.2]

    for strike in reasonable_strikes[:10]:  # Limit to first 10 strikes to avoid too many requests
        for expiry in chain.expirations[:3]:  # Limit to first 3 expirations
            # Create option contracts for both calls and puts
            for right in ['C', 'P']:
                try:
                    option = Option(stock.symbol, expiry, strike, right, 'SMART', tradingClass=chain.tradingClass)
                    qualified_contracts = ib.qualifyContracts(option)

                    if not qualified_contracts:
                        print(f"Could not qualify contract: {option}")
                        continue

                    option = qualified_contracts[0]  # Use the qualified contract

                    # Request market data
                    ticker = ib.reqMktData(option)
                    ib.sleep(0.5)  # Give more time for data to arrive

                    if ticker.bid and ticker.ask and ticker.bid > 0 and ticker.ask > 0:
                        # Calculate break even percentage
                        option_price = (ticker.ask + ticker.bid) / 2

                        if right == 'C':
                            break_even = ((strike + option_price) / current_stock_price - 1) * 100
                        else:
                            break_even = ((strike - option_price) / current_stock_price - 1) * 100

                        all_options.append({
                            'Strike': strike,
                            'Expiration': expiry,
                            'Right': right,
                            'Bid': ticker.bid,
                            'Ask': ticker.ask,
                            'Mid_Price': option_price,
                            'Break_Even_%': break_even
                        })

                        print(f"Added: {right} {strike} {expiry} - Bid: {ticker.bid}, Ask: {ticker.ask}")

                    # Cancel market data to free up resources
                    ib.cancelMktData(option)

                except Exception as e:
                    print(f"Error processing option {right} {strike} {expiry}: {e}")
                    continue

# Create DataFrame
options_df = pd.DataFrame(all_options)

# Sort by expiration and strike
options_df = options_df.sort_values(['Expiration', 'Strike'])

# Display the first few rows
print(options_df.head())

# Disconnect from IB
ib.disconnect()
