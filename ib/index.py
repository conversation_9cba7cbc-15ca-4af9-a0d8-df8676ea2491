from ib_insync import *
import pandas as pd

# Connect to IB
ib = IB()
ib.connect('127.0.0.1', 7496, clientId=1)

# Define the contract for AAPL stock
stock = Stock('AAPL', 'SMART', 'USD')
ib.qualifyContracts(stock)

# Request all available option chains
chains = ib.reqSecDefOptParams(stock.symbol, '', stock.secType, stock.conId)

# Initialize lists to store option data
all_options = []

# Process each option chain
for chain in chains:
    for strike in chain.strikes:
        for expiry in chain.expirations:
            # Create option contracts for both calls and puts
            for right in ['C', 'P']:
                option = Option(stock.symbol, expiry, strike, right, 'SMART', tradingClass=chain.tradingClass)
                ib.qualifyContracts(option)

                # Request market data
                ticker = ib.reqMktData(option)
                ib.sleep(0.1)  # Give time for data to arrive

                if ticker.bid and ticker.ask and ticker.last:
                    # Calculate break even percentage
                    if right == 'C':
                        break_even = ((strike + (ticker.ask + ticker.bid)/2) / stock.marketPrice() - 1) * 100
                    else:
                        break_even = ((strike - (ticker.ask + ticker.bid)/2) / stock.marketPrice() - 1) * 100

                    all_options.append({
                        'Strike': strike,
                        'Expiration': expiry,
                        'Right': right,
                        'Bid': ticker.bid,
                        'Ask': ticker.ask,
                        'Break_Even_%': break_even
                    })

# Create DataFrame
options_df = pd.DataFrame(all_options)

# Sort by expiration and strike
options_df = options_df.sort_values(['Expiration', 'Strike'])

# Display the first few rows
print(options_df.head())

# Disconnect from IB
ib.disconnect()
